<?php $__env->startSection('content'); ?>

<div class="container px-auto">
  <?php if (isset($component)) { $__componentOriginalb9eddf53444261b5c229e9d8b9f1298e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e = $attributes; } ?>
<?php $component = App\View\Components\Navbar::resolve(['brand' => 'Quantum Transition','class' => 'px-4 md:px-20 shadow-lg bg-black fixed top-0 z-50','links' => [
          ['label' => 'Inicio', 'href' => route('home')],
          ['label' => 'Cursos', 'href' => route('products.index')],
          ['label' => 'Sobre Mi', 'href' => '#contacto']
      ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Navbar::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e)): ?>
<?php $attributes = $__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e; ?>
<?php unset($__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb9eddf53444261b5c229e9d8b9f1298e)): ?>
<?php $component = $__componentOriginalb9eddf53444261b5c229e9d8b9f1298e; ?>
<?php unset($__componentOriginalb9eddf53444261b5c229e9d8b9f1298e); ?>
<?php endif; ?>
</div>


<section class="relative w-full h-screen overflow-hidden">
  
  <video autoplay muted loop playsinline class="absolute inset-0 w-full h-full object-cover z-10">
    <source src="<?php echo e(asset('media/videos/test.mp4')); ?>" type="video/mp4">
    Tu navegador no soporta la reproducción de video.
  </video>

  
  <div class="absolute inset-0 bg-black bg-opacity-50 z-9"></div>

  
  <div class="relative z-20 flex flex-col items-center justify-center h-full text-center text-white px-4">
    <h1 class="text-4xl md:text-6xl font-bold mb-4 drop-shadow-lg">Título principal impactante</h1>
    <p class="mb-8 text-lg md:text-2xl drop-shadow-md">Subtítulo con enfoque en desarrollo personal y transformación</p>
    <a href="#courses" class="bg-white text-black font-semibold px-6 py-3 rounded hover:bg-gray-200 transition">
      Acceder a cursos
    </a>
  </div>
</section>


<section id="features" class="py-16 space-y-16">
    <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if (isset($component)) { $__componentOriginaldf187c71fc38a3841f2a07f6afcc91c7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldf187c71fc38a3841f2a07f6afcc91c7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.feature','data' => ['title' => $feature->titulo,'description' => $feature->contenido,'image' => $feature->imagen,'reverse' => $loop->index % 2 !== 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('feature'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($feature->titulo),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($feature->contenido),'image' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($feature->imagen),'reverse' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($loop->index % 2 !== 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldf187c71fc38a3841f2a07f6afcc91c7)): ?>
<?php $attributes = $__attributesOriginaldf187c71fc38a3841f2a07f6afcc91c7; ?>
<?php unset($__attributesOriginaldf187c71fc38a3841f2a07f6afcc91c7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldf187c71fc38a3841f2a07f6afcc91c7)): ?>
<?php $component = $__componentOriginaldf187c71fc38a3841f2a07f6afcc91c7; ?>
<?php unset($__componentOriginaldf187c71fc38a3841f2a07f6afcc91c7); ?>
<?php endif; ?>  
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</section>


<section id="testimonials" class="py-16 bg-gray-100">
  <div class="container mx-auto">
    <h2 class="text-2xl font-bold mb-8 text-center">Testimonios</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  
        <?php if (isset($component)) { $__componentOriginal8deb93165fddf37f6c143395a1a4ac09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8deb93165fddf37f6c143395a1a4ac09 = $attributes; } ?>
<?php $component = App\View\Components\TestimonialCard::resolve(['contenido' => $testimonial->contenido,'imagen' => $testimonial->imagen,'nombre' => $testimonial->nombre,'cargo' => $testimonial->cargo,'empresa' => $testimonial->empresa] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('testimonial-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\TestimonialCard::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8deb93165fddf37f6c143395a1a4ac09)): ?>
<?php $attributes = $__attributesOriginal8deb93165fddf37f6c143395a1a4ac09; ?>
<?php unset($__attributesOriginal8deb93165fddf37f6c143395a1a4ac09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8deb93165fddf37f6c143395a1a4ac09)): ?>
<?php $component = $__componentOriginal8deb93165fddf37f6c143395a1a4ac09; ?>
<?php unset($__componentOriginal8deb93165fddf37f6c143395a1a4ac09); ?>
<?php endif; ?>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
  </div>
</section>


<section id="featured-product" class="py-16 bg-gray-50">
  <div class="container mx-auto px-4 md:px-20">
    <h2 class="text-3xl font-bold mb-8 text-center">Producto Destacado</h2>
    <div class="max-w-4xl mx-auto">
      <?php if($featuredProduct): ?>
        <?php if (isset($component)) { $__componentOriginal1ab624a366fca63e5670cfec6c2d0d4b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1ab624a366fca63e5670cfec6c2d0d4b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.featured-product','data' => ['product' => $featuredProduct]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('featured-product'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['product' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featuredProduct)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1ab624a366fca63e5670cfec6c2d0d4b)): ?>
<?php $attributes = $__attributesOriginal1ab624a366fca63e5670cfec6c2d0d4b; ?>
<?php unset($__attributesOriginal1ab624a366fca63e5670cfec6c2d0d4b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1ab624a366fca63e5670cfec6c2d0d4b)): ?>
<?php $component = $__componentOriginal1ab624a366fca63e5670cfec6c2d0d4b; ?>
<?php unset($__componentOriginal1ab624a366fca63e5670cfec6c2d0d4b); ?>
<?php endif; ?>
      <?php endif; ?>
    </div>
  </div>
</section>



<section class="cta py-16 bg-primary text-white text-center">
  <div class="container mx-auto">
    <h3 class="text-2xl font-bold mb-4">¿Listo para empezar tu transformación?</h3>
    <a href="#contact" class="btn btn-light">Contáctanos</a>
  </div>
</section>


<footer class="bg-gray-800 text-gray-200 py-8">
  <div class="container mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
    <div>
      <h5 class="font-bold mb-4">Quantum Transition</h5>
      <p>Texto breve sobre la misión o slogan.</p>
    </div>
    <div>
      <h5 class="font-bold mb-4">Enlaces</h5>
      <ul>
        <li><a href="#features" class="hover:underline">Servicios</a></li>
        <li><a href="#about" class="hover:underline">Equipo</a></li>
        <li><a href="#contact" class="hover:underline">Contacto</a></li>
      </ul>
    </div>
    <div id="contact">
      <h5 class="font-bold mb-4">Contacto</h5>
      <p><EMAIL></p>
      <p>+34 600 000 000</p>
    </div>
  </div>
</footer>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.landing', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/home.blade.php ENDPATH**/ ?>