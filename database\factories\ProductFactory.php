<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $nombre = fake()->words(3, true);
        return [
            'nombre' => $nombre,
            'slug' => Str::slug($nombre),
            'descripcion_corta' => fake()->sentence(),
            'descripcion_larga' => fake()->paragraph(3),
            'precio' => fake()->randomFloat(2, 10, 500),
            'precio_descuento' => fake()->randomFloat(2, 5, 400),
            'activo' => fake()->boolean(80),
            'orden' => fake()->numberBetween(1, 100),
        ];
    }
}