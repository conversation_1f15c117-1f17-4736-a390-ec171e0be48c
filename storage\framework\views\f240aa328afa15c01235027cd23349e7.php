<nav class="bg-gray-800 text-white py-4 w-full <?php echo e($class); ?>">
  <div class="container mx-auto flex justify-between items-center">
    <a href="/" class="text-xl font-bold"><?php echo e($brand); ?></a>
    <div class="flex items-center gap-4">
      <ul class="flex space-x-4 items-center">
        <?php $__currentLoopData = $links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <li><a href="<?php echo e($link['href']); ?>" class="hover:underline"><?php echo e($link['label']); ?></a></li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('cart-icon', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-4074399835-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>


        <?php if(Route::has('login')): ?>
          <?php if(auth()->guard()->check()): ?>
              <li>
                <?php if(Auth::user()->hasRole('admin')): ?>
                  <a href="<?php echo e(route('filament.admin.pages.dashboard')); ?>" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border border-transparent hover:border-[#3E3E3A] rounded-sm leading-normal">
                      Admin
                  </a>
                <?php else: ?>
                <a href="<?php echo e(url('/dashboard')); ?>" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border border-transparent hover:border-[#3E3E3A] rounded-sm leading-normal">
                    Dashboard
                </a>
                <?php endif; ?>
              </li>
              <li>
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                  <?php echo csrf_field(); ?>
                  <button type="submit" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border hover:border-[#3E3E3A] rounded-sm leading-normal">
                    Cerrar sesión
                  </button>
                </form>
              </li>
          <?php else: ?>
              <li>
                <a href="<?php echo e(route('login')); ?>" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border hover:border-[#3E3E3A] rounded-sm leading-normal">
                    Log in
                </a>
              </li>
              <?php if(Route::has('register')): ?>
                  <li>
                    <a href="<?php echo e(route('register')); ?>" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border hover:border-[#3E3E3A] rounded-sm leading-normal">
                        Register
                    </a>
                  </li>
              <?php endif; ?>
          <?php endif; ?>
        <?php endif; ?>
      </ul>
      <?php if (isset($component)) { $__componentOriginale07f4c7a5dde437a723f3b4366fbf32f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale07f4c7a5dde437a723f3b4366fbf32f = $attributes; } ?>
<?php $component = App\View\Components\LanguageSelector::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('language-selector'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\LanguageSelector::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale07f4c7a5dde437a723f3b4366fbf32f)): ?>
<?php $attributes = $__attributesOriginale07f4c7a5dde437a723f3b4366fbf32f; ?>
<?php unset($__attributesOriginale07f4c7a5dde437a723f3b4366fbf32f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale07f4c7a5dde437a723f3b4366fbf32f)): ?>
<?php $component = $__componentOriginale07f4c7a5dde437a723f3b4366fbf32f; ?>
<?php unset($__componentOriginale07f4c7a5dde437a723f3b4366fbf32f); ?>
<?php endif; ?>
    </div>
  </div>
</nav>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/components/navbar.blade.php ENDPATH**/ ?>