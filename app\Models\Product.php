<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'nombre',
        'slug',
        'descripcion_corta',
        'descripcion_larga',
        'precio',
        'precio_descuento',
        'activo',
        'orden',
    ];

    public function files()
    {
        return $this->hasMany(File::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class);
    }

    public function languages()
    {
        return $this->belongsToMany(Language::class, 'pivot_products_language');
    }
}
