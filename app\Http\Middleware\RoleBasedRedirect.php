<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleBasedRedirect
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Solo aplicar redirección si el usuario está autenticado
        if (Auth::check()) {
            $user = Auth::user();
            
            // Si el usuario está en la ruta raíz o dashboard, redirigir según su rol
            if ($request->is('/') || $request->is('dashboard')) {
                if ($user->hasRole('admin')) {
                    return redirect('/admin');
                } elseif ($user->hasRole('cliente')) {
                    return redirect('/app');
                }
            }
            
            // Si el usuario está intentando acceder a un panel incorrecto, redirigir
            if ($request->is('admin') || $request->is('admin/*')) {
                if (!$user->hasRole('admin')) {
                    if ($user->hasRole('cliente')) {
                        return redirect('/app');
                    }
                    return redirect('/');
                }
            }
            
            if ($request->is('app') || $request->is('app/*')) {
                if (!$user->hasRole('cliente')) {
                    if ($user->hasRole('admin')) {
                        return redirect('/admin');
                    }
                    return redirect('/');
                }
            }
        }

        return $next($request);
    }
}
