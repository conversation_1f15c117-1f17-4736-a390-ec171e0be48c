<?php

namespace App\Filament\Auth\Pages;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Form;
use Filament\Pages\Auth\Login as BaseLogin;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class Login extends BaseLogin
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('email')
                    ->label(__('filament-panels::pages/auth/login.form.email.label'))
                    ->email()
                    ->required()
                    ->autocomplete()
                    ->autofocus()
                    ->extraInputAttributes(['tabindex' => 1]),
                TextInput::make('password')
                    ->label(__('filament-panels::pages/auth/login.form.password.label'))
                    ->password()
                    ->required()
                    ->extraInputAttributes(['tabindex' => 2]),
                Checkbox::make('remember')
                    ->label(__('filament-panels::pages/auth/login.form.remember.label')),
            ]);
    }

    public function authenticate(): ?LoginResponse
    {
        $data = $this->form->getState();

        if (!Auth::attempt([
            'email' => $data['email'],
            'password' => $data['password'],
        ], $data['remember'] ?? false)) {
            throw ValidationException::withMessages([
                'data.email' => __('filament-panels::pages/auth/login.messages.failed'),
            ]);
        }

        // Llamar al método padre para manejar la respuesta correctamente
        return parent::authenticate();
    }

    protected function getRedirectUrl(): string
    {
        $user = Auth::user();

        // Log para debugging
        Log::info('Login redirect for user: ' . ($user ? $user->email : 'null'));
        if ($user) {
            Log::info('User roles: ' . $user->roles->pluck('name')->implode(', '));
        }

        // Redirección basada en roles
        if ($user && $user->hasRole('admin')) {
            Log::info('Redirecting to admin panel');
            return '/admin';
        } elseif ($user && $user->hasRole('cliente')) {
            Log::info('Redirecting to app panel');
            return '/app';
        }

        // Fallback por si el usuario no tiene ningún rol específico
        Log::info('Redirecting to dashboard');
        return '/dashboard';
    }
}
