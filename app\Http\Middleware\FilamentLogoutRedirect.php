<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class FilamentLogoutRedirect
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verificar si es una petición POST de logout desde Filament
        if (
            $request->isMethod('POST') &&
            ($request->is('admin/logout') || $request->is('app/logout'))
        ) {

            // Procesar el logout
            $response = $next($request);

            // Si el logout fue exitoso (usuario ya no autenticado), redirigir a homepage
            if (!Auth::check()) {
                return redirect('/');
            }

            return $response;
        }

        return $next($request);
    }
}
