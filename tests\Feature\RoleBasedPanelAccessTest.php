<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class RoleBasedPanelAccessTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Crear roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'cliente']);
    }

    public function test_admin_user_can_access_admin_panel()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
    }

    public function test_client_user_can_access_app_panel()
    {
        $client = User::factory()->create();
        $client->assignRole('cliente');

        $response = $this->actingAs($client)->get('/app');

        $response->assertStatus(200);
    }

    public function test_admin_user_cannot_access_app_panel()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/app');

        // Filament devuelve 403 cuando el usuario no tiene acceso al panel
        $response->assertStatus(403);
    }

    public function test_client_user_cannot_access_admin_panel()
    {
        $client = User::factory()->create();
        $client->assignRole('cliente');

        $response = $this->actingAs($client)->get('/admin');

        // Filament devuelve 403 cuando el usuario no tiene acceso al panel
        $response->assertStatus(403);
    }

    public function test_login_redirects_admin_to_admin_panel()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $admin->assignRole('admin');

        $response = Livewire::test(\App\Livewire\Auth\Login::class)
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->call('login');

        $response->assertRedirect('/admin');
    }

    public function test_login_redirects_client_to_app_panel()
    {
        $client = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $client->assignRole('cliente');

        $response = Livewire::test(\App\Livewire\Auth\Login::class)
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->call('login');

        $response->assertRedirect('/app');
    }

    public function test_unauthenticated_user_redirected_to_login_for_admin_panel()
    {
        $response = $this->get('/admin');

        $response->assertRedirect('/admin/login');
    }

    public function test_unauthenticated_user_redirected_to_login_for_app_panel()
    {
        $response = $this->get('/app');

        $response->assertRedirect('/app/login');
    }
}
