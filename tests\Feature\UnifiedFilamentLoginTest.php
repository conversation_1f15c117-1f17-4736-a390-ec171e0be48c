<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UnifiedFilamentLoginTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Crear roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'cliente']);
    }

    public function test_unified_login_page_is_accessible()
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        $response->assertSee('Email');
        $response->assertSee('Password');
    }

    public function test_admin_login_redirects_to_admin_panel()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $admin->assignRole('admin');

        $response = Livewire::test(\App\Filament\Auth\Pages\Login::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'password',
            ])
            ->call('authenticate');

        $response->assertRedirect('/admin');
        $this->assertAuthenticatedAs($admin);
    }

    public function test_client_login_redirects_to_app_panel()
    {
        $client = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $client->assignRole('cliente');

        $response = Livewire::test(\App\Filament\Auth\Pages\Login::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'password',
            ])
            ->call('authenticate');

        $response->assertRedirect('/app');
        $this->assertAuthenticatedAs($client);
    }

    public function test_user_without_role_redirects_to_dashboard()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        // No asignar ningún rol

        $response = Livewire::test(\App\Filament\Auth\Pages\Login::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'password',
            ])
            ->call('authenticate');

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    public function test_invalid_credentials_show_error()
    {
        $response = Livewire::test(\App\Filament\Auth\Pages\Login::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ])
            ->call('authenticate');

        $response->assertHasFormErrors(['email']);
        $this->assertGuest();
    }

    public function test_admin_panel_requires_admin_role()
    {
        $client = User::factory()->create();
        $client->assignRole('cliente');

        $response = $this->actingAs($client)->get('/admin');

        $response->assertStatus(403);
    }

    public function test_app_panel_requires_client_role()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/app');

        $response->assertStatus(403);
    }
}
